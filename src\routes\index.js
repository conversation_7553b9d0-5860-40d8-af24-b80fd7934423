const express = require("express");
const authRoutes = require("./auth");
const userRoutes = require("./users");
const roleRoutes = require("./roles");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   - name: Authentication
 *     description: User authentication endpoints
 *   - name: Users
 *     description: User management endpoints
 *   - name: Roles
 *     description: Role management endpoints
 */

// Mount route modules
router.use("/auth", authRoutes);
router.use("/users", userRoutes);
router.use("/roles", roleRoutes);

/**
 * @swagger
 * /:
 *   get:
 *     summary: API root endpoint
 *     tags: [General]
 *     responses:
 *       200:
 *         description: API information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 version:
 *                   type: string
 *                 documentation:
 *                   type: string
 *                 endpoints:
 *                   type: object
 */
router.get("/", (req, res) => {
  res.json({
    message: "Node MySQL API Template",
    version: process.env.API_VERSION || "v1",
    documentation: "/api-docs",
    endpoints: {
      authentication: "/auth",
      users: "/users",
      roles: "/roles",
      health: "/health",
    },
    timestamp: new Date().toISOString(),
  });
});

module.exports = router;
