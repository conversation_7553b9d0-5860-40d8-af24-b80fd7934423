const { promisePool } = require('../config/database');
const logger = require('../utils/logger');

class RoleRepository {
  /**
   * Get all roles
   */
  async findAll() {
    try {
      const [rows] = await promisePool.execute(
        'SELECT id, name, description, permissions, is_active, created_at, updated_at FROM roles WHERE is_active = true ORDER BY name'
      );
      
      return rows.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: JSON.parse(role.permissions || '[]'),
        isActive: role.is_active,
        createdAt: role.created_at,
        updatedAt: role.updated_at
      }));
    } catch (error) {
      logger.error('Error finding all roles:', error);
      throw error;
    }
  }

  /**
   * Find role by ID
   */
  async findById(id) {
    try {
      const [rows] = await promisePool.execute(
        'SELECT * FROM roles WHERE id = ? AND is_active = true',
        [id]
      );
      
      if (rows.length === 0) {
        return null;
      }

      const role = rows[0];
      return {
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: JSON.parse(role.permissions || '[]'),
        isActive: role.is_active,
        createdAt: role.created_at,
        updatedAt: role.updated_at
      };
    } catch (error) {
      logger.error('Error finding role by ID:', error);
      throw error;
    }
  }

  /**
   * Find role by name
   */
  async findByName(name) {
    try {
      const [rows] = await promisePool.execute(
        'SELECT * FROM roles WHERE name = ? AND is_active = true',
        [name]
      );
      
      if (rows.length === 0) {
        return null;
      }

      const role = rows[0];
      return {
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: JSON.parse(role.permissions || '[]'),
        isActive: role.is_active,
        createdAt: role.created_at,
        updatedAt: role.updated_at
      };
    } catch (error) {
      logger.error('Error finding role by name:', error);
      throw error;
    }
  }

  /**
   * Create new role
   */
  async create(roleData) {
    const { name, description, permissions } = roleData;
    
    try {
      const [result] = await promisePool.execute(
        'INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)',
        [name, description, JSON.stringify(permissions || [])]
      );
      
      return await this.findById(result.insertId);
    } catch (error) {
      logger.error('Error creating role:', error);
      throw error;
    }
  }

  /**
   * Update role
   */
  async update(id, roleData) {
    const allowedFields = ['name', 'description', 'permissions', 'isActive'];
    const updateFields = [];
    const values = [];

    // Build dynamic update query
    Object.keys(roleData).forEach(key => {
      if (allowedFields.includes(key) && roleData[key] !== undefined) {
        if (key === 'permissions') {
          updateFields.push('permissions = ?');
          values.push(JSON.stringify(roleData[key]));
        } else if (key === 'isActive') {
          updateFields.push('is_active = ?');
          values.push(roleData[key]);
        } else {
          updateFields.push(`${key} = ?`);
          values.push(roleData[key]);
        }
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    values.push(id);

    try {
      await promisePool.execute(
        `UPDATE roles SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );
      
      return await this.findById(id);
    } catch (error) {
      logger.error('Error updating role:', error);
      throw error;
    }
  }

  /**
   * Soft delete role
   */
  async softDelete(id) {
    try {
      await promisePool.execute(
        'UPDATE roles SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );
      return true;
    } catch (error) {
      logger.error('Error soft deleting role:', error);
      throw error;
    }
  }

  /**
   * Get users with a specific role
   */
  async getUsersByRole(roleId) {
    try {
      const [rows] = await promisePool.execute(`
        SELECT u.id, u.email, u.first_name, u.last_name, ur.assigned_at
        FROM users u
        INNER JOIN user_roles ur ON u.id = ur.user_id
        WHERE ur.role_id = ? AND ur.is_active = true AND u.is_active = true
        ORDER BY u.first_name, u.last_name
      `, [roleId]);

      return rows.map(row => ({
        id: row.id,
        email: row.email,
        firstName: row.first_name,
        lastName: row.last_name,
        assignedAt: row.assigned_at
      }));
    } catch (error) {
      logger.error('Error getting users by role:', error);
      throw error;
    }
  }

  /**
   * Get role statistics
   */
  async getRoleStatistics() {
    try {
      const [roleStats] = await promisePool.execute(`
        SELECT 
          r.id,
          r.name,
          r.description,
          COUNT(ur.user_id) as user_count
        FROM roles r
        LEFT JOIN user_roles ur ON r.id = ur.role_id AND ur.is_active = true
        WHERE r.is_active = true
        GROUP BY r.id, r.name, r.description
        ORDER BY user_count DESC, r.name
      `);

      return roleStats.map(stat => ({
        id: stat.id,
        name: stat.name,
        description: stat.description,
        userCount: stat.user_count
      }));
    } catch (error) {
      logger.error('Error getting role statistics:', error);
      throw error;
    }
  }
}

module.exports = new RoleRepository();
