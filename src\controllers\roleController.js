const { body, param, validationResult } = require('express-validator');
const roleService = require('../services/roleService');
const logger = require('../utils/logger');

class RoleController {
  /**
   * Role validation rules
   */
  static createRoleValidation = [
    body('name')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Role name is required and must be less than 50 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must be less than 500 characters'),
    body('permissions')
      .optional()
      .isArray()
      .withMessage('Permissions must be an array')
  ];

  static updateRoleValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('Role ID must be a positive integer'),
    body('name')
      .optional()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Role name must be between 1 and 50 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must be less than 500 characters'),
    body('permissions')
      .optional()
      .isArray()
      .withMessage('Permissions must be an array')
  ];

  static roleIdValidation = [
    param('id')
      .isInt({ min: 1 })
      .withMessage('Role ID must be a positive integer')
  ];

  static assignRoleValidation = [
    param('userId')
      .isInt({ min: 1 })
      .withMessage('User ID must be a positive integer'),
    param('roleId')
      .isInt({ min: 1 })
      .withMessage('Role ID must be a positive integer')
  ];

  /**
   * Get all roles
   */
  getAllRoles = async (req, res) => {
    try {
      const roles = await roleService.getAllRoles();

      res.status(200).json({
        success: true,
        message: 'Roles retrieved successfully',
        data: { roles }
      });
    } catch (error) {
      logger.error('Get all roles controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve roles'
      });
    }
  };

  /**
   * Get role by ID
   */
  getRoleById = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(error => error.msg)
        });
      }

      const roleId = parseInt(req.params.id);
      const role = await roleService.getRoleById(roleId);

      res.status(200).json({
        success: true,
        message: 'Role retrieved successfully',
        data: { role }
      });
    } catch (error) {
      logger.error('Get role by ID controller error:', error);
      
      if (error.message === 'Role not found') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to retrieve role'
      });
    }
  };

  /**
   * Create new role
   */
  createRole = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(error => error.msg)
        });
      }

      const roleData = req.body;

      // Additional validation
      const validation = roleService.validateRoleData(roleData);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid role data',
          errors: validation.errors
        });
      }

      const role = await roleService.createRole(roleData);

      res.status(201).json({
        success: true,
        message: 'Role created successfully',
        data: { role }
      });
    } catch (error) {
      logger.error('Create role controller error:', error);
      
      if (error.message === 'Role with this name already exists') {
        return res.status(409).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to create role'
      });
    }
  };

  /**
   * Update role
   */
  updateRole = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(error => error.msg)
        });
      }

      const roleId = parseInt(req.params.id);
      const roleData = req.body;

      const role = await roleService.updateRole(roleId, roleData);

      res.status(200).json({
        success: true,
        message: 'Role updated successfully',
        data: { role }
      });
    } catch (error) {
      logger.error('Update role controller error:', error);
      
      if (error.message === 'Role not found') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      if (error.message === 'Role name is already taken') {
        return res.status(409).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to update role'
      });
    }
  };

  /**
   * Delete role
   */
  deleteRole = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(error => error.msg)
        });
      }

      const roleId = parseInt(req.params.id);
      const result = await roleService.deleteRole(roleId);

      res.status(200).json({
        success: true,
        message: result.message
      });
    } catch (error) {
      logger.error('Delete role controller error:', error);
      
      if (error.message === 'Role not found') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      if (error.message === 'Cannot delete role that is assigned to users') {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to delete role'
      });
    }
  };

  /**
   * Assign role to user
   */
  assignRoleToUser = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(error => error.msg)
        });
      }

      const userId = parseInt(req.params.userId);
      const roleId = parseInt(req.params.roleId);
      const assignedBy = req.user.id;

      const result = await roleService.assignRoleToUser(userId, roleId, assignedBy);

      res.status(200).json({
        success: true,
        message: result.message
      });
    } catch (error) {
      logger.error('Assign role to user controller error:', error);
      
      if (error.message === 'User not found' || error.message === 'Role not found') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to assign role'
      });
    }
  };

  /**
   * Remove role from user
   */
  removeRoleFromUser = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array().map(error => error.msg)
        });
      }

      const userId = parseInt(req.params.userId);
      const roleId = parseInt(req.params.roleId);

      const result = await roleService.removeRoleFromUser(userId, roleId);

      res.status(200).json({
        success: true,
        message: result.message
      });
    } catch (error) {
      logger.error('Remove role from user controller error:', error);
      
      if (error.message === 'User not found' || error.message === 'Role not found') {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to remove role'
      });
    }
  };

  /**
   * Get role statistics
   */
  getRoleStatistics = async (req, res) => {
    try {
      const statistics = await roleService.getRoleStatistics();

      res.status(200).json({
        success: true,
        message: 'Role statistics retrieved successfully',
        data: { statistics }
      });
    } catch (error) {
      logger.error('Get role statistics controller error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve role statistics'
      });
    }
  };
}

module.exports = new RoleController();
