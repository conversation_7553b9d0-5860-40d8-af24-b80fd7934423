const swaggerJsdoc = require("swagger-jsdoc");

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Node MySQL API Template",
      version: "1.0.0",
      description:
        "A comprehensive Node.js API template with MySQL database, authentication, and Swagger documentation",
      contact: {
        name: "API Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 3000}/api/${
          process.env.API_VERSION || "v1"
        }`,
        description: "Development server",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "Enter JWT token",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            id: {
              type: "integer",
              description: "User ID",
            },
            email: {
              type: "string",
              format: "email",
              description: "User email address",
            },
            firstName: {
              type: "string",
              description: "User first name",
            },
            lastName: {
              type: "string",
              description: "User last name",
            },
            phone: {
              type: "string",
              description: "User phone number",
            },
            dateOfBirth: {
              type: "string",
              format: "date",
              description: "User date of birth",
            },
            address: {
              type: "string",
              description: "User address",
            },
            isActive: {
              type: "boolean",
              description: "Whether user is active",
            },
            emailVerified: {
              type: "boolean",
              description: "Whether email is verified",
            },
            roles: {
              type: "array",
              items: {
                $ref: "#/components/schemas/UserRole",
              },
              description: "User roles",
            },
            createdAt: {
              type: "string",
              format: "date-time",
              description: "User creation timestamp",
            },
            updatedAt: {
              type: "string",
              format: "date-time",
              description: "User last update timestamp",
            },
          },
        },
        UserRole: {
          type: "object",
          properties: {
            id: {
              type: "integer",
              description: "Role ID",
            },
            name: {
              type: "string",
              description: "Role name",
            },
            description: {
              type: "string",
              description: "Role description",
            },
            permissions: {
              type: "array",
              items: {
                type: "string",
              },
              description: "Role permissions",
            },
            assignedAt: {
              type: "string",
              format: "date-time",
              description: "When role was assigned",
            },
          },
        },
        LoginRequest: {
          type: "object",
          required: ["email", "password"],
          properties: {
            email: {
              type: "string",
              format: "email",
              description: "User email address",
            },
            password: {
              type: "string",
              minLength: 6,
              description: "User password",
            },
          },
        },
        RegisterRequest: {
          type: "object",
          required: ["email", "password", "firstName", "lastName"],
          properties: {
            email: {
              type: "string",
              format: "email",
              description: "User email address",
            },
            password: {
              type: "string",
              minLength: 6,
              description: "User password",
            },
            firstName: {
              type: "string",
              description: "User first name",
            },
            lastName: {
              type: "string",
              description: "User last name",
            },
            phone: {
              type: "string",
              description: "User phone number",
            },
            dateOfBirth: {
              type: "string",
              format: "date",
              description: "User date of birth",
            },
            address: {
              type: "string",
              description: "User address",
            },
            roles: {
              type: "array",
              items: {
                type: "string",
                enum: [
                  "admin",
                  "principal",
                  "teacher",
                  "student",
                  "parent",
                  "staff",
                ],
              },
              description: "User roles (defaults to student)",
            },
          },
        },
        AuthResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
            },
            message: {
              type: "string",
            },
            data: {
              type: "object",
              properties: {
                user: {
                  $ref: "#/components/schemas/User",
                },
                token: {
                  type: "string",
                  description: "JWT access token",
                },
              },
            },
          },
        },
        ErrorResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: false,
            },
            message: {
              type: "string",
              description: "Error message",
            },
            errors: {
              type: "array",
              items: {
                type: "string",
              },
              description: "Detailed error messages",
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ["./src/routes/*.js", "./src/controllers/*.js"],
};

const specs = swaggerJsdoc(options);

module.exports = specs;
