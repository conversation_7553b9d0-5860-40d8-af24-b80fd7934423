const roleRepository = require('../repositories/roleRepository');
const userRepository = require('../repositories/userRepository');
const logger = require('../utils/logger');

class RoleService {
  /**
   * Get all roles
   */
  async getAllRoles() {
    try {
      return await roleRepository.findAll();
    } catch (error) {
      logger.error('Get all roles service error:', error);
      throw error;
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(roleId) {
    try {
      const role = await roleRepository.findById(roleId);
      if (!role) {
        throw new Error('Role not found');
      }
      return role;
    } catch (error) {
      logger.error('Get role by ID service error:', error);
      throw error;
    }
  }

  /**
   * Create new role
   */
  async createRole(roleData) {
    try {
      // Check if role name already exists
      const existingRole = await roleRepository.findByName(roleData.name);
      if (existingRole) {
        throw new Error('Role with this name already exists');
      }

      return await roleRepository.create(roleData);
    } catch (error) {
      logger.error('Create role service error:', error);
      throw error;
    }
  }

  /**
   * Update role
   */
  async updateRole(roleId, roleData) {
    try {
      const existingRole = await roleRepository.findById(roleId);
      if (!existingRole) {
        throw new Error('Role not found');
      }

      // If name is being updated, check if it's already taken
      if (roleData.name && roleData.name !== existingRole.name) {
        const nameExists = await roleRepository.findByName(roleData.name);
        if (nameExists) {
          throw new Error('Role name is already taken');
        }
      }

      return await roleRepository.update(roleId, roleData);
    } catch (error) {
      logger.error('Update role service error:', error);
      throw error;
    }
  }

  /**
   * Delete role
   */
  async deleteRole(roleId) {
    try {
      const role = await roleRepository.findById(roleId);
      if (!role) {
        throw new Error('Role not found');
      }

      // Check if role is assigned to any users
      const usersWithRole = await roleRepository.getUsersByRole(roleId);
      if (usersWithRole.length > 0) {
        throw new Error('Cannot delete role that is assigned to users');
      }

      await roleRepository.softDelete(roleId);
      return { message: 'Role deleted successfully' };
    } catch (error) {
      logger.error('Delete role service error:', error);
      throw error;
    }
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(userId, roleId, assignedBy = null) {
    try {
      // Verify user exists
      const user = await userRepository.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify role exists
      const role = await roleRepository.findById(roleId);
      if (!role) {
        throw new Error('Role not found');
      }

      await userRepository.assignRolesToUser(userId, [role.name], assignedBy);
      
      logger.info(`Role '${role.name}' assigned to user ${userId} by ${assignedBy || 'system'}`);
      
      return { message: 'Role assigned successfully' };
    } catch (error) {
      logger.error('Assign role to user service error:', error);
      throw error;
    }
  }

  /**
   * Remove role from user
   */
  async removeRoleFromUser(userId, roleId) {
    try {
      // Verify user exists
      const user = await userRepository.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify role exists
      const role = await roleRepository.findById(roleId);
      if (!role) {
        throw new Error('Role not found');
      }

      await userRepository.removeRolesFromUser(userId, [role.name]);
      
      logger.info(`Role '${role.name}' removed from user ${userId}`);
      
      return { message: 'Role removed successfully' };
    } catch (error) {
      logger.error('Remove role from user service error:', error);
      throw error;
    }
  }

  /**
   * Get users by role
   */
  async getUsersByRole(roleId) {
    try {
      const role = await roleRepository.findById(roleId);
      if (!role) {
        throw new Error('Role not found');
      }

      return await roleRepository.getUsersByRole(roleId);
    } catch (error) {
      logger.error('Get users by role service error:', error);
      throw error;
    }
  }

  /**
   * Get role statistics
   */
  async getRoleStatistics() {
    try {
      return await roleRepository.getRoleStatistics();
    } catch (error) {
      logger.error('Get role statistics service error:', error);
      throw error;
    }
  }

  /**
   * Check if user has permission
   */
  async userHasPermission(userId, permission) {
    try {
      const user = await userRepository.findById(userId);
      if (!user) {
        return false;
      }

      // Check if user has any role with the required permission
      for (const role of user.roles) {
        if (role.permissions.includes('*') || role.permissions.includes(permission)) {
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.error('Check user permission service error:', error);
      return false;
    }
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId) {
    try {
      const user = await userRepository.findById(userId);
      if (!user) {
        return [];
      }

      const permissions = new Set();
      
      for (const role of user.roles) {
        role.permissions.forEach(permission => permissions.add(permission));
      }

      return Array.from(permissions);
    } catch (error) {
      logger.error('Get user permissions service error:', error);
      throw error;
    }
  }

  /**
   * Validate role data
   */
  validateRoleData(data) {
    const errors = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Role name is required');
    }

    if (data.name && data.name.length > 50) {
      errors.push('Role name must be less than 50 characters');
    }

    if (data.description && data.description.length > 500) {
      errors.push('Role description must be less than 500 characters');
    }

    if (data.permissions && !Array.isArray(data.permissions)) {
      errors.push('Permissions must be an array');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = new RoleService();
