const { body, validationResult } = require("express-validator");
const authService = require("../services/authService");
const logger = require("../utils/logger");

class AuthController {
  /**
   * Register validation rules
   */
  static registerValidation = [
    body("email")
      .isEmail()
      .normalizeEmail()
      .withMessage("Please provide a valid email address"),
    body("password")
      .isLength({ min: 6 })
      .withMessage("Password must be at least 6 characters long"),
    body("firstName")
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage(
        "First name is required and must be less than 100 characters"
      ),
    body("lastName")
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage(
        "Last name is required and must be less than 100 characters"
      ),
  ];

  /**
   * Login validation rules
   */
  static loginValidation = [
    body("email")
      .isEmail()
      .normalizeEmail()
      .withMessage("Please provide a valid email address"),
    body("password").notEmpty().withMessage("Password is required"),
  ];

  /**
   * Change password validation rules
   */
  static changePasswordValidation = [
    body("currentPassword")
      .notEmpty()
      .withMessage("Current password is required"),
    body("newPassword")
      .isLength({ min: 6 })
      .withMessage("New password must be at least 6 characters long"),
  ];

  /**
   * Register a new user
   */
  register = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const { email, password, firstName, lastName } = req.body;

      // Validate password strength
      const passwordValidation = authService.validatePassword(password);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: "Password does not meet requirements",
          errors: passwordValidation.errors,
        });
      }

      // Register user
      const result = await authService.register({
        email,
        password,
        firstName,
        lastName,
      });

      res.status(201).json({
        success: true,
        message: "User registered successfully",
        data: result,
      });
    } catch (error) {
      logger.error("Register controller error:", error);

      if (error.message === "User with this email already exists") {
        return res.status(409).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Registration failed",
      });
    }
  };

  /**
   * Login user
   */
  login = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const { email, password } = req.body;

      // Login user
      const result = await authService.login(email, password);

      res.status(200).json({
        success: true,
        message: "Login successful",
        data: result,
      });
    } catch (error) {
      logger.error("Login controller error:", error);

      if (error.message === "Invalid email or password") {
        return res.status(401).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Login failed",
      });
    }
  };

  /**
   * Change user password
   */
  changePassword = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const { currentPassword, newPassword } = req.body;
      const userId = req.user.id;

      // Validate new password strength
      const passwordValidation = authService.validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          success: false,
          message: "New password does not meet requirements",
          errors: passwordValidation.errors,
        });
      }

      // Change password
      const result = await authService.changePassword(
        userId,
        currentPassword,
        newPassword
      );

      res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      logger.error("Change password controller error:", error);

      if (error.message === "Current password is incorrect") {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Password change failed",
      });
    }
  };

  /**
   * Get current user profile
   */
  getProfile = async (req, res) => {
    try {
      res.status(200).json({
        success: true,
        message: "Profile retrieved successfully",
        data: { user: req.user },
      });
    } catch (error) {
      logger.error("Get profile controller error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve profile",
      });
    }
  };

  /**
   * Logout user (client-side token removal)
   */
  logout = async (req, res) => {
    try {
      // In a JWT-based system, logout is typically handled client-side
      // by removing the token from storage. This endpoint can be used
      // for logging purposes or token blacklisting if implemented.

      logger.info(`User logged out: ${req.user.email}`);

      res.status(200).json({
        success: true,
        message: "Logout successful",
      });
    } catch (error) {
      logger.error("Logout controller error:", error);
      res.status(500).json({
        success: false,
        message: "Logout failed",
      });
    }
  };

  /**
   * Initialize database (development endpoint)
   */
  initializeDatabase = async (req, res) => {
    try {
      if (process.env.NODE_ENV === "production") {
        return res.status(403).json({
          success: false,
          message: "Database initialization not allowed in production",
        });
      }

      await authService.initializeDatabase();

      res.status(200).json({
        success: true,
        message: "Database initialized successfully",
      });
    } catch (error) {
      logger.error("Initialize database controller error:", error);
      res.status(500).json({
        success: false,
        message: "Database initialization failed",
      });
    }
  };
}

module.exports = new AuthController();
