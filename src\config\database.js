const mysql = require("mysql2");
const logger = require("../utils/logger");

// Database configuration
const dbConfig = {
  host: process.env.APP_DB_HOST,
  user: process.env.APP_DB_USER,
  password: process.env.APP_DB_PASSWORD,
  database: process.env.APP_DB_NAME,
  port: process.env.APP_DB_PORT || 3306,
  connectionLimit: 10,
  charset: "utf8mb4",
  ssl: {
    rejectUnauthorized: false,
  },
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Create promise-based pool
const promisePool = pool.promise();

// Test database connection
const testConnection = async () => {
  try {
    const connection = await promisePool.getConnection();
    logger.info("Database connected successfully");
    connection.release();
    return true;
  } catch (error) {
    logger.error("Database connection failed:", error.message);
    return false;
  }
};

// Initialize database connection
testConnection();

// Handle connection errors
pool.on("connection", (connection) => {
  logger.info(
    `New database connection established as id ${connection.threadId}`
  );
});

pool.on("error", (err) => {
  logger.error("Database pool error:", err);
  if (err.code === "PROTOCOL_CONNECTION_LOST") {
    logger.info("Attempting to reconnect to database...");
    testConnection();
  } else {
    throw err;
  }
});

// Export both pool and promise pool
module.exports = {
  pool,
  promisePool,
  testConnection,
};
