const jwt = require("jsonwebtoken");
const logger = require("../utils/logger");
const userRepository = require("../repositories/userRepository");

/**
 * Middleware to verify JWT token and authenticate user
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "Access token is required",
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if user still exists
    const user = await userRepository.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: "User not found or token is invalid",
      });
    }

    // Remove password from user object
    delete user.password;

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    logger.error("Authentication error:", error);

    if (error.name === "JsonWebTokenError") {
      return res.status(401).json({
        success: false,
        message: "Invalid token",
      });
    }

    if (error.name === "TokenExpiredError") {
      return res.status(401).json({
        success: false,
        message: "Token has expired",
      });
    }

    return res.status(500).json({
      success: false,
      message: "Authentication failed",
    });
  }
};

/**
 * Middleware to check if user has specific role
 */
const requireRole = (requiredRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    if (!req.user.roles || req.user.roles.length === 0) {
      return res.status(403).json({
        success: false,
        message: "No roles assigned",
      });
    }

    // Check if user has any of the required roles
    const userRoleNames = req.user.roles.map((role) => role.name);
    const hasRequiredRole = requiredRoles.some((role) =>
      userRoleNames.includes(role)
    );

    if (!hasRequiredRole) {
      return res.status(403).json({
        success: false,
        message: "Insufficient permissions",
      });
    }

    next();
  };
};

/**
 * Middleware to check if user has specific permission
 */
const requirePermission = (requiredPermission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    if (!req.user.roles || req.user.roles.length === 0) {
      return res.status(403).json({
        success: false,
        message: "No roles assigned",
      });
    }

    // Check if user has the required permission
    const hasPermission = req.user.roles.some(
      (role) =>
        role.permissions.includes("*") ||
        role.permissions.includes(requiredPermission)
    );

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: "Insufficient permissions",
      });
    }

    next();
  };
};

/**
 * Optional middleware for routes that can work with or without authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1];

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await userRepository.findById(decoded.userId);

      if (user) {
        delete user.password;
        req.user = user;
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't return error, just continue without user
    logger.warn("Optional authentication failed:", error.message);
    next();
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  requirePermission,
  optionalAuth,
};
