const { promisePool } = require("../config/database");
const logger = require("../utils/logger");

class UserRepository {
  /**
   * Convert database format (snake_case) to JavaScript format (camelCase)
   */
  convertToJSFormat(dbRow) {
    if (!dbRow) return null;
    return {
      id: dbRow.id,
      email: dbRow.email,
      password: dbRow.password,
      firstName: dbRow.first_name,
      lastName: dbRow.last_name,
      phone: dbRow.phone,
      dateOfBirth: dbRow.date_of_birth,
      address: dbRow.address,
      isActive: dbRow.is_active,
      emailVerified: dbRow.email_verified,
      lastLoginAt: dbRow.last_login_at,
      createdAt: dbRow.created_at,
      updatedAt: dbRow.updated_at,
      roles: dbRow.roles || [], // Will be populated when joining with roles
    };
  }

  /**
   * Convert JavaScript format (camelCase) to database format (snake_case)
   */
  convertToDBFormat(jsObj) {
    const dbObj = {};
    if (jsObj.firstName !== undefined) dbObj.first_name = jsObj.firstName;
    if (jsObj.lastName !== undefined) dbObj.last_name = jsObj.lastName;
    if (jsObj.phone !== undefined) dbObj.phone = jsObj.phone;
    if (jsObj.dateOfBirth !== undefined)
      dbObj.date_of_birth = jsObj.dateOfBirth;
    if (jsObj.address !== undefined) dbObj.address = jsObj.address;
    if (jsObj.isActive !== undefined) dbObj.is_active = jsObj.isActive;
    if (jsObj.emailVerified !== undefined)
      dbObj.email_verified = jsObj.emailVerified;
    if (jsObj.email !== undefined) dbObj.email = jsObj.email;
    return dbObj;
  }

  /**
   * Drop all tables if they exist (for development)
   */
  async dropAllTables() {
    try {
      // Drop in reverse order due to foreign key constraints
      await promisePool.execute("DROP TABLE IF EXISTS user_roles");
      await promisePool.execute("DROP TABLE IF EXISTS users");
      await promisePool.execute("DROP TABLE IF EXISTS roles");
      logger.info("All tables dropped");
    } catch (error) {
      logger.error("Error dropping tables:", error);
      throw error;
    }
  }

  /**
   * Create roles table
   */
  async createRolesTable() {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        permissions JSON,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    try {
      await promisePool.execute(createTableQuery);
      logger.info("Roles table created or already exists");
    } catch (error) {
      logger.error("Error creating roles table:", error);
      throw error;
    }
  }

  /**
   * Create users table
   */
  async createUsersTable() {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        date_of_birth DATE,
        address TEXT,
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        last_login_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_active (is_active),
        INDEX idx_name (first_name, last_name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    try {
      await promisePool.execute(createTableQuery);
      logger.info("Users table created or already exists");
    } catch (error) {
      logger.error("Error creating users table:", error);
      throw error;
    }
  }

  /**
   * Create user_roles junction table
   */
  async createUserRolesTable() {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        role_id INT NOT NULL,
        assigned_by INT,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_user_role (user_id, role_id),
        INDEX idx_user_id (user_id),
        INDEX idx_role_id (role_id),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    try {
      await promisePool.execute(createTableQuery);
      logger.info("User roles table created or already exists");
    } catch (error) {
      logger.error("Error creating user roles table:", error);
      throw error;
    }
  }

  /**
   * Create all tables in correct order
   */
  async createAllTables() {
    try {
      await this.createRolesTable();
      await this.createUsersTable();
      await this.createUserRolesTable();
      await this.seedDefaultRoles();
      logger.info("All tables created successfully");
    } catch (error) {
      logger.error("Error creating tables:", error);
      throw error;
    }
  }

  /**
   * Seed default roles for school management system
   */
  async seedDefaultRoles() {
    const defaultRoles = [
      {
        name: "admin",
        description: "System administrator with full access",
        permissions: JSON.stringify(["*"]),
      },
      {
        name: "principal",
        description: "School principal with administrative access",
        permissions: JSON.stringify([
          "manage_school",
          "view_all_users",
          "manage_teachers",
          "manage_students",
        ]),
      },
      {
        name: "teacher",
        description: "Teacher with access to classes and students",
        permissions: JSON.stringify([
          "view_students",
          "manage_grades",
          "view_classes",
        ]),
      },
      {
        name: "student",
        description: "Student with limited access",
        permissions: JSON.stringify([
          "view_profile",
          "view_grades",
          "view_schedule",
        ]),
      },
      {
        name: "parent",
        description: "Parent with access to child information",
        permissions: JSON.stringify([
          "view_child_profile",
          "view_child_grades",
          "view_child_schedule",
        ]),
      },
      {
        name: "staff",
        description: "School staff member",
        permissions: JSON.stringify(["view_profile", "basic_access"]),
      },
    ];

    try {
      for (const role of defaultRoles) {
        // Check if role already exists
        const [existing] = await promisePool.execute(
          "SELECT id FROM roles WHERE name = ?",
          [role.name]
        );

        if (existing.length === 0) {
          await promisePool.execute(
            "INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)",
            [role.name, role.description, role.permissions]
          );
          logger.info(`Default role '${role.name}' created`);
        }
      }
    } catch (error) {
      logger.error("Error seeding default roles:", error);
      throw error;
    }
  }

  /**
   * Find user by ID with roles
   */
  async findById(id) {
    try {
      const [userRows] = await promisePool.execute(
        "SELECT * FROM users WHERE id = ? AND is_active = true",
        [id]
      );

      if (userRows.length === 0) {
        return null;
      }

      const user = this.convertToJSFormat(userRows[0]);

      // Get user roles
      const [roleRows] = await promisePool.execute(
        `
        SELECT r.id, r.name, r.description, r.permissions, ur.assigned_at
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ? AND ur.is_active = true AND r.is_active = true
      `,
        [id]
      );

      user.roles = roleRows.map((role) => ({
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: JSON.parse(role.permissions || "[]"),
        assignedAt: role.assigned_at,
      }));

      return user;
    } catch (error) {
      logger.error("Error finding user by ID:", error);
      throw error;
    }
  }

  /**
   * Find user by email with roles
   */
  async findByEmail(email) {
    try {
      const [userRows] = await promisePool.execute(
        "SELECT * FROM users WHERE email = ? AND is_active = true",
        [email]
      );

      if (userRows.length === 0) {
        return null;
      }

      const user = this.convertToJSFormat(userRows[0]);

      // Get user roles
      const [roleRows] = await promisePool.execute(
        `
        SELECT r.id, r.name, r.description, r.permissions, ur.assigned_at
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ? AND ur.is_active = true AND r.is_active = true
      `,
        [user.id]
      );

      user.roles = roleRows.map((role) => ({
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: JSON.parse(role.permissions || "[]"),
        assignedAt: role.assigned_at,
      }));

      return user;
    } catch (error) {
      logger.error("Error finding user by email:", error);
      throw error;
    }
  }

  /**
   * Create new user
   */
  async create(userData) {
    const {
      email,
      password,
      firstName,
      lastName,
      phone,
      dateOfBirth,
      address,
      roles = ["student"], // Default role
    } = userData;

    try {
      // Create user - handle undefined values by converting to null
      const [result] = await promisePool.execute(
        "INSERT INTO users (email, password, first_name, last_name, phone, date_of_birth, address) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [
          email,
          password,
          firstName,
          lastName,
          phone || null,
          dateOfBirth || null,
          address || null,
        ]
      );

      const userId = result.insertId;

      // Assign roles to user
      if (roles && roles.length > 0) {
        await this.assignRolesToUser(userId, roles);
      }

      return await this.findById(userId);
    } catch (error) {
      logger.error("Error creating user:", error);
      throw error;
    }
  }

  /**
   * Assign roles to a user
   */
  async assignRolesToUser(userId, roleNames, assignedBy = null) {
    try {
      for (const roleName of roleNames) {
        // Get role ID
        const [roleRows] = await promisePool.execute(
          "SELECT id FROM roles WHERE name = ? AND is_active = true",
          [roleName]
        );

        if (roleRows.length > 0) {
          const roleId = roleRows[0].id;

          // Check if assignment already exists
          const [existing] = await promisePool.execute(
            "SELECT id FROM user_roles WHERE user_id = ? AND role_id = ?",
            [userId, roleId]
          );

          if (existing.length === 0) {
            await promisePool.execute(
              "INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES (?, ?, ?)",
              [userId, roleId, assignedBy]
            );
            logger.info(`Role '${roleName}' assigned to user ${userId}`);
          }
        } else {
          logger.warn(`Role '${roleName}' not found`);
        }
      }
    } catch (error) {
      logger.error("Error assigning roles to user:", error);
      throw error;
    }
  }

  /**
   * Remove roles from a user
   */
  async removeRolesFromUser(userId, roleNames) {
    try {
      for (const roleName of roleNames) {
        const [roleRows] = await promisePool.execute(
          "SELECT id FROM roles WHERE name = ? AND is_active = true",
          [roleName]
        );

        if (roleRows.length > 0) {
          const roleId = roleRows[0].id;
          await promisePool.execute(
            "UPDATE user_roles SET is_active = false WHERE user_id = ? AND role_id = ?",
            [userId, roleId]
          );
          logger.info(`Role '${roleName}' removed from user ${userId}`);
        }
      }
    } catch (error) {
      logger.error("Error removing roles from user:", error);
      throw error;
    }
  }

  /**
   * Update user
   */
  async update(id, userData) {
    const allowedFields = [
      "firstName",
      "lastName",
      "email",
      "role",
      "isActive",
      "emailVerified",
    ];
    const updateFields = [];
    const values = [];

    // Build dynamic update query
    Object.keys(userData).forEach((key) => {
      if (allowedFields.includes(key) && userData[key] !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(userData[key]);
      }
    });

    if (updateFields.length === 0) {
      throw new Error("No valid fields to update");
    }

    values.push(id);

    try {
      await promisePool.execute(
        `UPDATE users SET ${updateFields.join(
          ", "
        )}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      logger.error("Error updating user:", error);
      throw error;
    }
  }

  /**
   * Update user password
   */
  async updatePassword(id, hashedPassword) {
    try {
      await promisePool.execute(
        "UPDATE users SET password = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?",
        [hashedPassword, id]
      );
      return true;
    } catch (error) {
      logger.error("Error updating user password:", error);
      throw error;
    }
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(id) {
    try {
      await promisePool.execute(
        "UPDATE users SET lastLoginAt = CURRENT_TIMESTAMP WHERE id = ?",
        [id]
      );
      return true;
    } catch (error) {
      logger.error("Error updating last login:", error);
      throw error;
    }
  }

  /**
   * Soft delete user (set isActive to false)
   */
  async softDelete(id) {
    try {
      await promisePool.execute(
        "UPDATE users SET isActive = false, updatedAt = CURRENT_TIMESTAMP WHERE id = ?",
        [id]
      );
      return true;
    } catch (error) {
      logger.error("Error soft deleting user:", error);
      throw error;
    }
  }

  /**
   * Get all users with pagination
   */
  async findAll(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = "WHERE isActive = true";
    const values = [];

    // Add filters
    if (filters.role) {
      whereClause += " AND role = ?";
      values.push(filters.role);
    }

    if (filters.search) {
      whereClause +=
        " AND (firstName LIKE ? OR lastName LIKE ? OR email LIKE ?)";
      const searchTerm = `%${filters.search}%`;
      values.push(searchTerm, searchTerm, searchTerm);
    }

    try {
      // Get total count
      const [countResult] = await promisePool.execute(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        values
      );
      const total = countResult[0].total;

      // Get users
      const [rows] = await promisePool.execute(
        `SELECT id, email, firstName, lastName, role, isActive, emailVerified, lastLoginAt, createdAt, updatedAt 
         FROM users ${whereClause} 
         ORDER BY createdAt DESC 
         LIMIT ? OFFSET ?`,
        [...values, limit, offset]
      );

      return {
        users: rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Error finding all users:", error);
      throw error;
    }
  }
}

module.exports = new UserRepository();
