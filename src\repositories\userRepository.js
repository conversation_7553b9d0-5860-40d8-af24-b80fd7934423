const { promisePool } = require("../config/database");
const logger = require("../utils/logger");

class UserRepository {
  /**
   * Convert database format (snake_case) to JavaScript format (camelCase)
   */
  convertToJSFormat(dbRow) {
    if (!dbRow) return null;
    return {
      id: dbRow.id,
      email: dbRow.email,
      password: dbRow.password,
      firstName: dbRow.first_name,
      lastName: dbRow.last_name,
      role: dbRow.role,
      isActive: dbRow.is_active,
      emailVerified: dbRow.email_verified,
      lastLoginAt: dbRow.last_login_at,
      createdAt: dbRow.created_at,
      updatedAt: dbRow.updated_at,
    };
  }

  /**
   * Convert JavaScript format (camelCase) to database format (snake_case)
   */
  convertToDBFormat(jsObj) {
    const dbObj = {};
    if (jsObj.firstName !== undefined) dbObj.first_name = jsObj.firstName;
    if (jsObj.lastName !== undefined) dbObj.last_name = jsObj.lastName;
    if (jsObj.isActive !== undefined) dbObj.is_active = jsObj.isActive;
    if (jsObj.emailVerified !== undefined)
      dbObj.email_verified = jsObj.emailVerified;
    if (jsObj.email !== undefined) dbObj.email = jsObj.email;
    if (jsObj.role !== undefined) dbObj.role = jsObj.role;
    return dbObj;
  }

  /**
   * Drop users table if it exists (for development)
   */
  async dropUsersTable() {
    try {
      await promisePool.execute("DROP TABLE IF EXISTS users");
      logger.info("Users table dropped");
    } catch (error) {
      logger.error("Error dropping users table:", error);
      throw error;
    }
  }

  /**
   * Create users table if it doesn't exist
   */
  async createUsersTable() {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        role ENUM('user', 'admin') DEFAULT 'user',
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        last_login_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    try {
      await promisePool.execute(createTableQuery);
      logger.info("Users table created or already exists");
    } catch (error) {
      logger.error("Error creating users table:", error);
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  async findById(id) {
    try {
      const [rows] = await promisePool.execute(
        "SELECT * FROM users WHERE id = ? AND is_active = true",
        [id]
      );
      return this.convertToJSFormat(rows[0]);
    } catch (error) {
      logger.error("Error finding user by ID:", error);
      throw error;
    }
  }

  /**
   * Find user by email
   */
  async findByEmail(email) {
    try {
      const [rows] = await promisePool.execute(
        "SELECT * FROM users WHERE email = ? AND is_active = true",
        [email]
      );
      return this.convertToJSFormat(rows[0]);
    } catch (error) {
      logger.error("Error finding user by email:", error);
      throw error;
    }
  }

  /**
   * Create new user
   */
  async create(userData) {
    const { email, password, firstName, lastName, role = "user" } = userData;

    try {
      const [result] = await promisePool.execute(
        "INSERT INTO users (email, password, first_name, last_name, role) VALUES (?, ?, ?, ?, ?)",
        [email, password, firstName, lastName, role]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      logger.error("Error creating user:", error);
      throw error;
    }
  }

  /**
   * Update user
   */
  async update(id, userData) {
    const allowedFields = [
      "firstName",
      "lastName",
      "email",
      "role",
      "isActive",
      "emailVerified",
    ];
    const updateFields = [];
    const values = [];

    // Build dynamic update query
    Object.keys(userData).forEach((key) => {
      if (allowedFields.includes(key) && userData[key] !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(userData[key]);
      }
    });

    if (updateFields.length === 0) {
      throw new Error("No valid fields to update");
    }

    values.push(id);

    try {
      await promisePool.execute(
        `UPDATE users SET ${updateFields.join(
          ", "
        )}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      logger.error("Error updating user:", error);
      throw error;
    }
  }

  /**
   * Update user password
   */
  async updatePassword(id, hashedPassword) {
    try {
      await promisePool.execute(
        "UPDATE users SET password = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?",
        [hashedPassword, id]
      );
      return true;
    } catch (error) {
      logger.error("Error updating user password:", error);
      throw error;
    }
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(id) {
    try {
      await promisePool.execute(
        "UPDATE users SET lastLoginAt = CURRENT_TIMESTAMP WHERE id = ?",
        [id]
      );
      return true;
    } catch (error) {
      logger.error("Error updating last login:", error);
      throw error;
    }
  }

  /**
   * Soft delete user (set isActive to false)
   */
  async softDelete(id) {
    try {
      await promisePool.execute(
        "UPDATE users SET isActive = false, updatedAt = CURRENT_TIMESTAMP WHERE id = ?",
        [id]
      );
      return true;
    } catch (error) {
      logger.error("Error soft deleting user:", error);
      throw error;
    }
  }

  /**
   * Get all users with pagination
   */
  async findAll(page = 1, limit = 10, filters = {}) {
    const offset = (page - 1) * limit;
    let whereClause = "WHERE isActive = true";
    const values = [];

    // Add filters
    if (filters.role) {
      whereClause += " AND role = ?";
      values.push(filters.role);
    }

    if (filters.search) {
      whereClause +=
        " AND (firstName LIKE ? OR lastName LIKE ? OR email LIKE ?)";
      const searchTerm = `%${filters.search}%`;
      values.push(searchTerm, searchTerm, searchTerm);
    }

    try {
      // Get total count
      const [countResult] = await promisePool.execute(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        values
      );
      const total = countResult[0].total;

      // Get users
      const [rows] = await promisePool.execute(
        `SELECT id, email, firstName, lastName, role, isActive, emailVerified, lastLoginAt, createdAt, updatedAt 
         FROM users ${whereClause} 
         ORDER BY createdAt DESC 
         LIMIT ? OFFSET ?`,
        [...values, limit, offset]
      );

      return {
        users: rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Error finding all users:", error);
      throw error;
    }
  }
}

module.exports = new UserRepository();
