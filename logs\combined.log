{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:19:28'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:19:28'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api-docs',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:19:28'
}
{
  message: 'New database connection established as id 23469',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:19:29'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:19:29'
}
{
  message: 'Users table created or already exists',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:22:43'
}
{
  message: 'Database tables initialized successfully',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:22:43'
}
{
  service: 'node-mysql-api',
  message: "Error creating user: Unknown column 'firstName' in 'field list'",
  code: 'ER_BAD_FIELD_ERROR',
  errno: 1054,
  sql: 'INSERT INTO users (email, password, firstName, lastName, role) VALUES (?, ?, ?, ?, ?)',
  sqlState: '42S22',
  sqlMessage: "Unknown column 'firstName' in 'field list'",
  level: 'error',
  stack: "Error: Unknown column 'firstName' in 'field list'\n" +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:75:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:82:22)',
  timestamp: '2025-06-02 14:22:50'
}
{
  service: 'node-mysql-api',
  message: "Registration error: Unknown column 'firstName' in 'field list'",
  code: 'ER_BAD_FIELD_ERROR',
  errno: 1054,
  sql: 'INSERT INTO users (email, password, firstName, lastName, role) VALUES (?, ?, ?, ?, ?)',
  sqlState: '42S22',
  sqlMessage: "Unknown column 'firstName' in 'field list'",
  level: 'error',
  stack: "Error: Unknown column 'firstName' in 'field list'\n" +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:75:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:82:22)',
  timestamp: '2025-06-02 14:22:50'
}
{
  service: 'node-mysql-api',
  message: "Register controller error: Unknown column 'firstName' in 'field list'",
  code: 'ER_BAD_FIELD_ERROR',
  errno: 1054,
  sql: 'INSERT INTO users (email, password, firstName, lastName, role) VALUES (?, ?, ?, ?, ?)',
  sqlState: '42S22',
  sqlMessage: "Unknown column 'firstName' in 'field list'",
  level: 'error',
  stack: "Error: Unknown column 'firstName' in 'field list'\n" +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:75:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:82:22)',
  timestamp: '2025-06-02 14:22:50'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:26:07'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:26:07'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api-docs',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:26:07'
}
{
  message: 'New database connection established as id 23472',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:26:08'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 14:26:08'
}
{
  service: 'node-mysql-api',
  message: "Error dropping users table: Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.",
  code: 'ER_FK_CANNOT_DROP_PARENT',
  errno: 3730,
  sql: 'DROP TABLE IF EXISTS users',
  sqlState: 'HY000',
  sqlMessage: "Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.",
  level: 'error',
  stack: "Error: Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.\n" +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.dropUsersTable (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:45:25)\n' +
    '    at AuthService.initializeDatabase (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:198:28)\n' +
    '    at initializeDatabase (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:264:25)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\index.js:346:12)',
  timestamp: '2025-06-02 14:26:24'
}
{
  service: 'node-mysql-api',
  message: "Database initialization error: Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.",
  code: 'ER_FK_CANNOT_DROP_PARENT',
  errno: 3730,
  sql: 'DROP TABLE IF EXISTS users',
  sqlState: 'HY000',
  sqlMessage: "Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.",
  level: 'error',
  stack: "Error: Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.\n" +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.dropUsersTable (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:45:25)\n' +
    '    at AuthService.initializeDatabase (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:198:28)\n' +
    '    at initializeDatabase (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:264:25)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\index.js:346:12)',
  timestamp: '2025-06-02 14:26:24'
}
{
  service: 'node-mysql-api',
  message: "Initialize database controller error: Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.",
  code: 'ER_FK_CANNOT_DROP_PARENT',
  errno: 3730,
  sql: 'DROP TABLE IF EXISTS users',
  sqlState: 'HY000',
  sqlMessage: "Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.",
  level: 'error',
  stack: "Error: Cannot drop table 'users' referenced by a foreign key constraint 'fk_userIdToken' on table 'access_token'.\n" +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.dropUsersTable (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:45:25)\n' +
    '    at AuthService.initializeDatabase (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:198:28)\n' +
    '    at initializeDatabase (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:264:25)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\express\\lib\\router\\index.js:346:12)',
  timestamp: '2025-06-02 14:26:24'
}
{
  message: 'Server is running on port 3001',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:15'
}
{
  message: 'Environment: development',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:15'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api-docs',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:15'
}
{
  message: 'New database connection established as id 23560',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:16'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:16'
}
{
  message: 'All tables dropped',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:33'
}
{
  message: 'Roles table created or already exists',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:33'
}
{
  message: 'Users table created or already exists',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: 'User roles table created or already exists',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: "Default role 'admin' created",
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: "Default role 'principal' created",
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: "Default role 'teacher' created",
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: "Default role 'student' created",
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: "Default role 'parent' created",
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: "Default role 'staff' created",
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: 'All tables created successfully',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  message: 'Database tables initialized successfully',
  level: 'info',
  service: 'node-mysql-api',
  timestamp: '2025-06-02 18:48:34'
}
{
  service: 'node-mysql-api',
  message: 'Error creating user: Bind parameters must not contain undefined. To pass SQL NULL specify JS null',
  code: undefined,
  errno: undefined,
  sql: undefined,
  sqlState: undefined,
  sqlMessage: undefined,
  level: 'error',
  stack: 'Error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null\n' +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:347:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:91:22)',
  timestamp: '2025-06-02 18:48:50'
}
{
  service: 'node-mysql-api',
  message: 'Registration error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null',
  code: undefined,
  errno: undefined,
  sql: undefined,
  sqlState: undefined,
  sqlMessage: undefined,
  level: 'error',
  stack: 'Error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null\n' +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:347:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:91:22)',
  timestamp: '2025-06-02 18:48:50'
}
{
  service: 'node-mysql-api',
  message: 'Register controller error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null',
  code: undefined,
  errno: undefined,
  sql: undefined,
  sqlState: undefined,
  sqlMessage: undefined,
  level: 'error',
  stack: 'Error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null\n' +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:347:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:91:22)',
  timestamp: '2025-06-02 18:48:50'
}
{
  service: 'node-mysql-api',
  message: 'Error creating user: Bind parameters must not contain undefined. To pass SQL NULL specify JS null',
  code: undefined,
  errno: undefined,
  sql: undefined,
  sqlState: undefined,
  sqlMessage: undefined,
  level: 'error',
  stack: 'Error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null\n' +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:347:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:91:22)',
  timestamp: '2025-06-02 18:49:36'
}
{
  service: 'node-mysql-api',
  message: 'Registration error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null',
  code: undefined,
  errno: undefined,
  sql: undefined,
  sqlState: undefined,
  sqlMessage: undefined,
  level: 'error',
  stack: 'Error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null\n' +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:347:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:91:22)',
  timestamp: '2025-06-02 18:49:36'
}
{
  service: 'node-mysql-api',
  message: 'Register controller error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null',
  code: undefined,
  errno: undefined,
  sql: undefined,
  sqlState: undefined,
  sqlMessage: undefined,
  level: 'error',
  stack: 'Error: Bind parameters must not contain undefined. To pass SQL NULL specify JS null\n' +
    '    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n' +
    '    at UserRepository.create (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\repositories\\userRepository.js:347:42)\n' +
    '    at AuthService.register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\services\\authService.js:25:44)\n' +
    '    at async register (C:\\Users\\<USER>\\Documents\\Projects\\mode-mysql-api-template\\src\\controllers\\authController.js:91:22)',
  timestamp: '2025-06-02 18:49:36'
}
