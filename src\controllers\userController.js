const { body, query, param, validationResult } = require("express-validator");
const userService = require("../services/userService");
const logger = require("../utils/logger");

class UserController {
  /**
   * Update profile validation rules
   */
  static updateProfileValidation = [
    body("firstName")
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage("First name must be between 1 and 100 characters"),
    body("lastName")
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage("Last name must be between 1 and 100 characters"),
    body("email")
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage("Please provide a valid email address"),
  ];

  /**
   * Get users validation rules
   */
  static getUsersValidation = [
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("limit")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Limit must be between 1 and 100"),
    query("role")
      .optional()
      .isIn(["user", "admin"])
      .withMessage('Role must be either "user" or "admin"'),
    query("search")
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage("Search term must not be empty"),
  ];

  /**
   * User ID validation
   */
  static userIdValidation = [
    param("id")
      .isInt({ min: 1 })
      .withMessage("User ID must be a positive integer"),
  ];

  /**
   * Update role validation
   */
  static updateRoleValidation = [
    param("id")
      .isInt({ min: 1 })
      .withMessage("User ID must be a positive integer"),
    body("role")
      .isIn(["user", "admin"])
      .withMessage('Role must be either "user" or "admin"'),
  ];

  /**
   * Get current user profile
   */
  getProfile = async (req, res) => {
    try {
      const user = await userService.getUserProfile(req.user.id);

      res.status(200).json({
        success: true,
        message: "Profile retrieved successfully",
        data: { user },
      });
    } catch (error) {
      logger.error("Get profile controller error:", error);

      if (error.message === "User not found") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Failed to retrieve profile",
      });
    }
  };

  /**
   * Update current user profile
   */
  updateProfile = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const updateData = req.body;
      const userId = req.user.id;

      // Validate update data
      const validation = userService.validateUpdateData(updateData);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: "Invalid update data",
          errors: validation.errors,
        });
      }

      const updatedUser = await userService.updateUserProfile(
        userId,
        updateData
      );

      res.status(200).json({
        success: true,
        message: "Profile updated successfully",
        data: { user: updatedUser },
      });
    } catch (error) {
      logger.error("Update profile controller error:", error);

      if (error.message === "User not found") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message === "Email is already taken by another user") {
        return res.status(409).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Failed to update profile",
      });
    }
  };

  /**
   * Get all users (admin only)
   */
  getAllUsers = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const filters = {};

      if (req.query.role) filters.role = req.query.role;
      if (req.query.search) filters.search = req.query.search;

      const result = await userService.getAllUsers(page, limit, filters);

      res.status(200).json({
        success: true,
        message: "Users retrieved successfully",
        data: result,
      });
    } catch (error) {
      logger.error("Get all users controller error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve users",
      });
    }
  };

  /**
   * Get user by ID (admin only)
   */
  getUserById = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const userId = parseInt(req.params.id);
      const user = await userService.getUserById(userId);

      res.status(200).json({
        success: true,
        message: "User retrieved successfully",
        data: { user },
      });
    } catch (error) {
      logger.error("Get user by ID controller error:", error);

      if (error.message === "User not found") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Failed to retrieve user",
      });
    }
  };

  /**
   * Update user role (admin only)
   */
  updateUserRole = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const userId = parseInt(req.params.id);
      const { role } = req.body;

      const updatedUser = await userService.updateUserRole(userId, role);

      res.status(200).json({
        success: true,
        message: "User role updated successfully",
        data: { user: updatedUser },
      });
    } catch (error) {
      logger.error("Update user role controller error:", error);

      if (error.message === "User not found") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes("Invalid role")) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Failed to update user role",
      });
    }
  };

  /**
   * Deactivate user (admin only)
   */
  deactivateUser = async (req, res) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array().map((error) => error.msg),
        });
      }

      const userId = parseInt(req.params.id);

      // Prevent admin from deactivating themselves
      if (userId === req.user.id) {
        return res.status(400).json({
          success: false,
          message: "You cannot deactivate your own account",
        });
      }

      const result = await userService.deactivateUser(userId);

      res.status(200).json({
        success: true,
        message: result.message,
      });
    } catch (error) {
      logger.error("Deactivate user controller error:", error);

      if (error.message === "User not found") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Failed to deactivate user",
      });
    }
  };

  /**
   * Search users (admin only)
   */
  searchUsers = async (req, res) => {
    try {
      const searchTerm = req.query.q;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      if (!searchTerm) {
        return res.status(400).json({
          success: false,
          message: "Search term is required",
        });
      }

      const result = await userService.searchUsers(searchTerm, page, limit);

      res.status(200).json({
        success: true,
        message: "Search completed successfully",
        data: result,
      });
    } catch (error) {
      logger.error("Search users controller error:", error);
      res.status(500).json({
        success: false,
        message: "Search failed",
      });
    }
  };

  /**
   * Get user statistics (admin only)
   */
  getUserStatistics = async (req, res) => {
    try {
      const stats = await userService.getUserStatistics();

      res.status(200).json({
        success: true,
        message: "Statistics retrieved successfully",
        data: { statistics: stats },
      });
    } catch (error) {
      logger.error("Get user statistics controller error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve statistics",
      });
    }
  };
}

module.exports = new UserController();
